import request from '@/utils/request'

// 分页查询
export function getInWaterPage(data) {
  return request({
    url: '/model/in-water/page',
    method: 'post',
    data,
  })
}

// 删除
export function deleteInWater(params) {
  return request({
    url: '/model/in-water/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 指标下的站点信息
export function getIndexCodeSites(params) {
  return request({
    url: '/base/site/getIndexCodeSites',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 站点配置-列表
export function getInWaterRange(params) {
  return request({
    url: '/model/in-water/range/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 站点配置-设置
export function setInWaterRange(data) {
  return request({
    url: '/model/in-water/range/set',
    method: 'post',
    data,
  })
}

// 获取降雨过程
export function getRainfallList(data) {
  return request({
    url: '/model/in-water/rainfall/list',
    method: 'post',
    data,
  })
}

// 执行预报
export function forecast(data) {
  return request({
    url: '/model/in-water/forecast',
    method: 'post',
    data,
  })
}

// 详情
export function getInWater(params) {
  return request({
    url: '/model/in-water/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 保存
export function saveInWater(params) {
  return request({
    url: '/model/in-water/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 自动预报查询
export function queryAutoForecast(data) {
  return request({
    url: '/model/in-water/auto-forecast/query',
    method: 'post',
    data,
  })
}

// 获取来水模型详细信息
export function getInWaterRes(params) {
  return request({
    url: '/model/in-water/getInWaterRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
